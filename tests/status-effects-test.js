// Comprehensive Status Effects Test Suite
// Tests status effect application, persistence, feedback, and clearing mechanics

class StatusEffectsTest {
  constructor() {
    this.testResults = [];
    this.mockBattleSystem = null;
    this.mockPokemon = null;
  }

  // Create mock Pokemon for testing
  createMockPokemon(name, level = 50) {
    const mockData = {
      id: 1,
      name: name,
      types: ["normal"],
      baseStats: {
        hp: 100,
        attack: 80,
        defense: 80,
        specialAttack: 80,
        specialDefense: 80,
        speed: 80,
      },
      sprites: { front: "", back: "" },
      height: 10,
      weight: 100,
      moves: [
        {
          name: "tackle",
          power: 40,
          accuracy: 100,
          pp: 35,
          type: "normal",
          damageClass: "physical",
        },
        {
          name: "sleep-powder",
          power: null,
          accuracy: 75,
          pp: 15,
          type: "grass",
          damageClass: "status",
        },
        {
          name: "toxic",
          power: null,
          accuracy: 90,
          pp: 10,
          type: "poison",
          damageClass: "status",
        },
        {
          name: "thunder-wave",
          power: null,
          accuracy: 90,
          pp: 20,
          type: "electric",
          damageClass: "status",
        },
      ],
    };
    return new Pokemon(mockData, level);
  }

  // Mock battle system for testing
  createMockBattleSystem() {
    return {
      messages: [],
      statusUpdates: [],

      showMessage: function (message, duration = 2000) {
        this.messages.push({ message, duration, timestamp: Date.now() });
        console.log(`[BATTLE MESSAGE]: ${message}`);
        return Promise.resolve();
      },

      updateStatusDisplay: function (side, pokemon) {
        this.statusUpdates.push({
          side,
          pokemon: pokemon.name,
          status: pokemon.status,
          confusion: pokemon.confusion,
          timestamp: Date.now(),
        });
        console.log(
          `[STATUS UPDATE]: ${side} - ${pokemon.name} - Status: ${
            pokemon.status || "none"
          }, Confused: ${pokemon.confusion}`
        );
      },

      updatePokemonDisplay: function (side, pokemon) {
        console.log(
          `[POKEMON UPDATE]: ${side} - ${pokemon.name} - HP: ${pokemon.currentHP}/${pokemon.maxHP}`
        );
      },
    };
  }

  // Test 1: Status Effect Application
  async testStatusApplication() {
    console.log("\n=== Test 1: Status Effect Application ===");
    const pokemon = this.createMockPokemon("testmon");
    const results = [];

    // Test poison application
    const poisonResult = pokemon.setStatus("poison");
    results.push({
      test: "Poison application",
      expected: true,
      actual: poisonResult,
      passed: poisonResult === true,
    });

    // Test that second status fails (can't have multiple primary status)
    const burnResult = pokemon.setStatus("burn");
    results.push({
      test: "Second status blocked",
      expected: false,
      actual: burnResult,
      passed: burnResult === false,
    });

    // Test confusion (secondary status)
    const confusionResult = pokemon.setConfusion();
    results.push({
      test: "Confusion with existing status",
      expected: true,
      actual: confusionResult,
      passed: confusionResult === true,
    });

    this.testResults.push({ category: "Status Application", results });
    return results;
  }

  // Test 2: Status Effect Persistence During Switching
  async testStatusPersistence() {
    console.log("\n=== Test 2: Status Effect Persistence ===");
    const pokemon1 = this.createMockPokemon("pokemon1");
    const pokemon2 = this.createMockPokemon("pokemon2");
    const results = [];

    // Apply status effects
    pokemon1.setStatus("poison");
    pokemon1.setConfusion();

    // Simulate switching out (status should persist)
    const statusBeforeSwitch = pokemon1.status;
    const confusionBeforeSwitch = pokemon1.confusion;

    // Pokemon is switched out but not fainted - status persists
    results.push({
      test: "Status persists when switched out",
      expected: "poison",
      actual: pokemon1.status,
      passed: pokemon1.status === "poison",
    });

    results.push({
      test: "Confusion persists when switched out",
      expected: true,
      actual: pokemon1.confusion,
      passed: pokemon1.confusion === true,
    });

    this.testResults.push({ category: "Status Persistence", results });
    return results;
  }

  // Test 3: Status Effect Clearing on Fainting
  async testStatusClearingOnFaint() {
    console.log("\n=== Test 3: Status Clearing on Fainting ===");
    const pokemon = this.createMockPokemon("testmon");
    const results = [];

    // Apply status effects
    pokemon.setStatus("burn");
    pokemon.setConfusion();

    // Simulate fainting
    pokemon.takeDamage(pokemon.maxHP);

    results.push({
      test: "Status cleared on fainting",
      expected: null,
      actual: pokemon.status,
      passed: pokemon.status === null,
    });

    results.push({
      test: "Confusion cleared on fainting",
      expected: false,
      actual: pokemon.confusion,
      passed: pokemon.confusion === false,
    });

    results.push({
      test: "Pokemon is fainted",
      expected: true,
      actual: pokemon.fainted,
      passed: pokemon.fainted === true,
    });

    this.testResults.push({ category: "Status Clearing on Faint", results });
    return results;
  }

  // Test 4: End-of-Turn Status Processing
  async testEndOfTurnProcessing() {
    console.log("\n=== Test 4: End-of-Turn Processing ===");
    const pokemon = this.createMockPokemon("testmon");
    const results = [];

    // Test poison damage
    pokemon.setStatus("poison");
    const hpBeforePoison = pokemon.currentHP;
    const effects = pokemon.processEndOfTurn();

    const expectedDamage = Math.floor(
      pokemon.maxHP * CONFIG.BATTLE.STATUS_DAMAGE.POISON
    );
    const actualDamage = hpBeforePoison - pokemon.currentHP;

    results.push({
      test: "Poison damage calculation",
      expected: expectedDamage,
      actual: actualDamage,
      passed: actualDamage === expectedDamage,
    });

    results.push({
      test: "Poison effect reported",
      expected: "status_damage",
      actual: effects[0]?.type,
      passed: effects[0]?.type === "status_damage",
    });

    // Test sleep countdown
    const sleepPokemon = this.createMockPokemon("sleeper");
    sleepPokemon.setStatus("sleep");
    sleepPokemon.statusTurns = 1; // Will clear next turn

    const sleepEffects = sleepPokemon.processEndOfTurn();

    results.push({
      test: "Sleep status cleared after countdown",
      expected: null,
      actual: sleepPokemon.status,
      passed: sleepPokemon.status === null,
    });

    this.testResults.push({ category: "End-of-Turn Processing", results });
    return results;
  }

  // Test 5: Status Effect Feedback Messages
  async testStatusFeedback() {
    console.log("\n=== Test 5: Status Effect Feedback ===");
    const battleSystem = this.createMockBattleSystem();
    const pokemon = this.createMockPokemon("testmon");
    const results = [];

    // Test status display update
    pokemon.setStatus("paralysis");
    battleSystem.updateStatusDisplay("player", pokemon);

    const lastStatusUpdate =
      battleSystem.statusUpdates[battleSystem.statusUpdates.length - 1];

    results.push({
      test: "Status display updated",
      expected: "paralysis",
      actual: lastStatusUpdate?.status,
      passed: lastStatusUpdate?.status === "paralysis",
    });

    // Test status text generation
    const statusText = pokemon.getStatusText();
    results.push({
      test: "Status text generation",
      expected: "PARALYSIS",
      actual: statusText,
      passed: statusText === "PARALYSIS",
    });

    // Test combined status and confusion text
    pokemon.setConfusion();
    const combinedText = pokemon.getStatusText();
    results.push({
      test: "Combined status text",
      expected: "PARALYSIS, CONFUSED",
      actual: combinedText,
      passed: combinedText === "PARALYSIS, CONFUSED",
    });

    this.testResults.push({ category: "Status Feedback", results });
    return results;
  }

  // Test 6: Move Blocking by Status Effects
  async testMoveBlocking() {
    console.log("\n=== Test 6: Move Blocking by Status ===");
    const pokemon = this.createMockPokemon("testmon");
    const results = [];

    // Test sleep blocking moves
    pokemon.setStatus("sleep");
    const canUseMoveWhileSleep = pokemon.canUseMove(0);

    results.push({
      test: "Sleep blocks move usage",
      expected: false,
      actual: canUseMoveWhileSleep,
      passed: canUseMoveWhileSleep === false,
    });

    // Test freeze blocking moves
    pokemon.clearStatus();
    pokemon.setStatus("freeze");
    const canUseMoveWhileFrozen = pokemon.canUseMove(0);

    results.push({
      test: "Freeze blocks move usage",
      expected: false,
      actual: canUseMoveWhileFrozen,
      passed: canUseMoveWhileFrozen === false,
    });

    // Test paralysis (probabilistic blocking)
    pokemon.clearStatus();
    pokemon.setStatus("paralysis");

    // Run multiple tests for paralysis (25% chance to block)
    let blockedCount = 0;
    let totalTests = 100;

    for (let i = 0; i < totalTests; i++) {
      if (!pokemon.canUseMove(0)) {
        blockedCount++;
      }
    }

    const blockRate = blockedCount / totalTests;
    const expectedRate = 0.25;
    const tolerance = 0.1; // 10% tolerance for randomness

    results.push({
      test: "Paralysis blocking rate (~25%)",
      expected: `${expectedRate} ± ${tolerance}`,
      actual: blockRate.toFixed(2),
      passed: Math.abs(blockRate - expectedRate) <= tolerance,
    });

    this.testResults.push({ category: "Move Blocking", results });
    return results;
  }

  // Test 7: Confusion Self-Damage
  async testConfusionSelfDamage() {
    console.log("\n=== Test 7: Confusion Self-Damage ===");
    const pokemon = this.createMockPokemon("testmon");
    const results = [];

    pokemon.setConfusion();

    // Test confusion self-damage (50% chance)
    let selfDamageCount = 0;
    let totalTests = 100;

    for (let i = 0; i < totalTests; i++) {
      const testPokemon = this.createMockPokemon("test");
      testPokemon.setConfusion();
      const hpBefore = testPokemon.currentHP;
      const moveResult = testPokemon.useMove(0);

      if (moveResult && moveResult.type === "confusion_self_damage") {
        selfDamageCount++;
      }
    }

    const selfDamageRate = selfDamageCount / totalTests;
    const expectedRate = 0.5;
    const tolerance = 0.1;

    results.push({
      test: "Confusion self-damage rate (~50%)",
      expected: `${expectedRate} ± ${tolerance}`,
      actual: selfDamageRate.toFixed(2),
      passed: Math.abs(selfDamageRate - expectedRate) <= tolerance,
    });

    this.testResults.push({ category: "Confusion Self-Damage", results });
    return results;
  }

  // Test 8: Status Move Application
  async testStatusMoveApplication() {
    console.log("\n=== Test 8: Status Move Application ===");
    const battleSystem = this.createMockBattleSystem();
    const attacker = this.createMockPokemon("attacker");
    const target = this.createMockPokemon("target");
    const results = [];

    // Mock the applyStatusMove method
    const applyStatusMove = async (attacker, target, move) => {
      const statusMoveEffects = {
        "sleep-powder": {
          status: "sleep",
          message: `${target.name} fell asleep!`,
        },
        "stun-spore": {
          status: "paralysis",
          message: `${target.name} was paralyzed!`,
        },
        "poison-powder": {
          status: "poison",
          message: `${target.name} was poisoned!`,
        },
        "confuse-ray": {
          confusion: true,
          message: `${target.name} became confused!`,
        },
      };

      const effect = statusMoveEffects[move.name];
      if (!effect) {
        return { success: false, message: `${move.name} has no effect!` };
      }

      if (effect.status) {
        const applied = target.setStatus(effect.status);
        return applied
          ? { success: true, message: effect.message }
          : {
              success: false,
              message: `${target.name} is already ${target.status}!`,
            };
      }

      if (effect.confusion) {
        const applied = target.setConfusion();
        return applied
          ? { success: true, message: effect.message }
          : { success: false, message: `${target.name} is already confused!` };
      }

      return { success: false, message: "But it failed!" };
    };

    // Test Sleep Powder
    const sleepMove = {
      name: "sleep-powder",
      type: "grass",
      damageClass: "status",
    };
    const sleepResult = await applyStatusMove(attacker, target, sleepMove);

    results.push({
      test: "Sleep Powder application",
      expected: true,
      actual: sleepResult.success && target.status === "sleep",
      passed: sleepResult.success && target.status === "sleep",
    });

    // Test status move on already affected Pokemon
    const sleepResult2 = await applyStatusMove(attacker, target, sleepMove);

    results.push({
      test: "Status move blocked by existing status",
      expected: false,
      actual: sleepResult2.success,
      passed: sleepResult2.success === false,
    });

    // Test Confuse Ray on new target
    const newTarget = this.createMockPokemon("newtarget");
    const confuseMove = {
      name: "confuse-ray",
      type: "ghost",
      damageClass: "status",
    };
    const confuseResult = await applyStatusMove(
      attacker,
      newTarget,
      confuseMove
    );

    results.push({
      test: "Confuse Ray application",
      expected: true,
      actual: confuseResult.success && newTarget.confusion,
      passed: confuseResult.success && newTarget.confusion,
    });

    this.testResults.push({ category: "Status Move Application", results });
    return results;
  }

  // Run all tests
  async runAllTests() {
    console.log("🧪 Starting Comprehensive Status Effects Test Suite");
    console.log("=".repeat(60));

    await this.testStatusApplication();
    await this.testStatusPersistence();
    await this.testStatusClearingOnFaint();
    await this.testEndOfTurnProcessing();
    await this.testStatusFeedback();
    await this.testMoveBlocking();
    await this.testConfusionSelfDamage();
    await this.testStatusMoveApplication();

    this.generateReport();
  }

  // Generate test report
  generateReport() {
    console.log("\n" + "=".repeat(60));
    console.log("📊 TEST RESULTS SUMMARY");
    console.log("=".repeat(60));

    let totalTests = 0;
    let passedTests = 0;

    this.testResults.forEach((category) => {
      console.log(`\n${category.category}:`);
      category.results.forEach((result) => {
        totalTests++;
        if (result.passed) passedTests++;

        const status = result.passed ? "✅ PASS" : "❌ FAIL";
        console.log(`  ${status} - ${result.test}`);
        if (!result.passed) {
          console.log(`    Expected: ${result.expected}`);
          console.log(`    Actual: ${result.actual}`);
        }
      });
    });

    console.log("\n" + "=".repeat(60));
    console.log(
      `📈 OVERALL RESULTS: ${passedTests}/${totalTests} tests passed (${(
        (passedTests / totalTests) *
        100
      ).toFixed(1)}%)`
    );

    if (passedTests === totalTests) {
      console.log(
        "🎉 All tests passed! Status effects system is working correctly."
      );
    } else {
      console.log("⚠️  Some tests failed. Review the implementation.");
    }

    return {
      total: totalTests,
      passed: passedTests,
      failed: totalTests - passedTests,
      percentage: (passedTests / totalTests) * 100,
    };
  }
}

// Export for use in browser or Node.js
if (typeof module !== "undefined" && module.exports) {
  module.exports = StatusEffectsTest;
} else if (typeof window !== "undefined") {
  window.StatusEffectsTest = StatusEffectsTest;
}
